import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { gridPreviewApi } from '@/api/otoconfig/gridconfig';
import type { GridPreviewVO, GridItemVO } from '@/api/otoconfig/gridconfig/types';

import { debounce } from '@/utils';

/**
 * 金刚位预览组合式函数
 * 提供实时预览、响应式布局、设备模拟等功能
 */
export function useGridPreview() {
  // 响应式数据
  const previewLoading = ref(false);
  const previewData = ref<GridPreviewVO | null>(null);
  const previewVisible = ref(false);

  // 预览配置
  const previewConfig = reactive({
    platform: 'ALL', // ALL, IOS, ANDROID, H5
    deviceType: 'mobile', // mobile, tablet, desktop
    orientation: 'portrait', // portrait, landscape
    theme: 'light', // light, dark
    showGrid: true, // 是否显示网格线
    showLabels: true, // 是否显示标签
    scale: 1, // 缩放比例
    groupId: undefined as number | undefined // 预览特定分组
  });

  // 设备尺寸配置
  const deviceSizes = {
    mobile: {
      portrait: { width: 375, height: 667 },
      landscape: { width: 667, height: 375 }
    },
    tablet: {
      portrait: { width: 768, height: 1024 },
      landscape: { width: 1024, height: 768 }
    },
    desktop: {
      portrait: { width: 1200, height: 800 },
      landscape: { width: 1200, height: 800 }
    }
  };

    /**
   * 获取预览数据
   */
  const getPreviewData = async () => {
    try {
      previewLoading.value = true;

      const params = {
        platform: previewConfig.platform,
        groupId: previewConfig.groupId
      };

      const response = await gridPreviewApi.getByPlatform(params.platform || '');

      if (response.code === 200) {
        previewData.value = response.data;
      } else {
        ElMessage.error(response.msg || '获取预览数据失败');
      }
    } catch (error) {
      console.error('获取预览数据失败:', error);
      ElMessage.error('获取预览数据失败，请稍后重试');
    } finally {
      previewLoading.value = false;
    }
  };

  /**
   * 防抖的预览数据获取
   */
  const debouncedGetPreviewData = debounce(
    () => {
      getPreviewData();
    },
    300,
    false
  );

  /**
   * 打开预览
   */
  const openPreview = async (groupId?: number) => {
    if (groupId !== undefined) {
      previewConfig.groupId = groupId;
    }
    previewVisible.value = true;
    await nextTick();
    await getPreviewData();
  };

  /**
   * 关闭预览
   */
  const closePreview = () => {
    previewVisible.value = false;
    previewData.value = null;
  };

  /**
   * 刷新预览
   */
  const refreshPreview = () => {
    debouncedGetPreviewData();
  };

  /**
   * 切换平台
   */
  const switchPlatform = (platform: string) => {
    previewConfig.platform = platform;
    debouncedGetPreviewData();
  };

  /**
   * 切换设备类型
   */
  const switchDeviceType = (deviceType: string) => {
    previewConfig.deviceType = deviceType;
    // 设备类型变化时，重置为竖屏
    if (deviceType !== 'desktop') {
      previewConfig.orientation = 'portrait';
    }
  };

  /**
   * 切换屏幕方向
   */
  const switchOrientation = (orientation: string) => {
    previewConfig.orientation = orientation;
  };

  /**
   * 切换主题
   */
  const switchTheme = (theme: string) => {
    previewConfig.theme = theme;
  };

  /**
   * 设置缩放比例
   */
  const setScale = (scale: number) => {
    previewConfig.scale = Math.max(0.5, Math.min(2, scale));
  };

  /**
   * 切换网格显示
   */
  const toggleGrid = () => {
    previewConfig.showGrid = !previewConfig.showGrid;
  };

  /**
   * 切换标签显示
   */
  const toggleLabels = () => {
    previewConfig.showLabels = !previewConfig.showLabels;
  };

  /**
   * 重置预览配置
   */
  const resetPreviewConfig = () => {
    Object.assign(previewConfig, {
      platform: 'ALL',
      deviceType: 'mobile',
      orientation: 'portrait',
      theme: 'light',
      showGrid: true,
      showLabels: true,
      scale: 1,
      groupId: undefined
    });

  };

  /**
   * 获取网格项目样式
   */
  const getGridItemStyle = (item: GridItemVO, index: number) => {
    const baseStyle: any = {
      transition: 'all 0.3s ease',
      cursor: 'pointer'
    };

    // 根据设备类型调整样式
    if (previewConfig.deviceType === 'mobile') {
      baseStyle.width = '60px';
      baseStyle.height = '60px';
      baseStyle.fontSize = '12px';
    } else if (previewConfig.deviceType === 'tablet') {
      baseStyle.width = '80px';
      baseStyle.height = '80px';
      baseStyle.fontSize = '14px';
    } else {
      baseStyle.width = '100px';
      baseStyle.height = '100px';
      baseStyle.fontSize = '16px';
    }    // 主题样式
    if (previewConfig.theme === 'dark') {
      baseStyle.backgroundColor = '#2d2d2d';
      baseStyle.color = '#ffffff';
      baseStyle.border = '1px solid #404040';
    } else {
      baseStyle.backgroundColor = '#ffffff';
      baseStyle.color = '#333333';
      baseStyle.border = '1px solid #e0e0e0';
    }

    // 禁用状态
    if (item.isActive === '1') {
      baseStyle.opacity = 0.5;
      baseStyle.cursor = 'not-allowed';
    }

    return baseStyle;
  };

  /**
   * 获取预览容器样式
   */
  const getPreviewContainerStyle = computed(() => {
    const size = deviceSizes[previewConfig.deviceType as keyof typeof deviceSizes][previewConfig.orientation as keyof typeof deviceSizes.mobile];

    return {
      width: `${size.width * previewConfig.scale}px`,
      height: `${size.height * previewConfig.scale}px`,
      transform: `scale(${previewConfig.scale})`,
      transformOrigin: 'top left',
      backgroundColor: previewConfig.theme === 'dark' ? '#1a1a1a' : '#f5f5f5',
      border: '1px solid #ccc',
      borderRadius: '8px',
      overflow: 'hidden',
      position: 'relative'
    };
  });

  /**
   * 获取网格容器样式
   */
  const getGridContainerStyle = computed(() => {
    const style: any = {
      display: 'grid',
      gap: '10px',
      padding: '20px',
      height: '100%',
      overflow: 'auto'
    };

    // 根据设备类型设置网格列数
    if (previewConfig.deviceType === 'mobile') {
      style.gridTemplateColumns = 'repeat(4, 1fr)';
    } else if (previewConfig.deviceType === 'tablet') {
      style.gridTemplateColumns = 'repeat(6, 1fr)';
    } else {
      style.gridTemplateColumns = 'repeat(8, 1fr)';
    }

    // 网格线
    if (previewConfig.showGrid) {
      style.backgroundImage = `
        linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px)
      `;
      style.backgroundSize = '60px 60px';
    }

    return style;
  });



  // 监听平台和分组变化，自动刷新预览
  watch(
    () => [previewConfig.platform, previewConfig.groupId],
    () => {
      if (previewVisible.value) {
        debouncedGetPreviewData();
      }
    }
  );

  return {
    // 响应式数据
    previewLoading,
    previewData,
    previewVisible,
    previewConfig,
    deviceSizes,

    // 计算属性
    getPreviewContainerStyle,
    getGridContainerStyle,

    // 方法
    getPreviewData,
    openPreview,
    closePreview,
    refreshPreview,
    switchPlatform,
    switchDeviceType,
    switchOrientation,
    switchTheme,
    setScale,
    toggleGrid,
    toggleLabels,
    resetPreviewConfig,
    getGridItemStyle
  };
}
