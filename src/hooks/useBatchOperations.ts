import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import { delGridItem, updateGridItem, batchUpdateGridItemSort } from '@/api/otoconfig/gridconfig';
import type { GridItemVO, GridItemForm, BatchSortRequest } from '@/api/otoconfig/gridconfig/types';

/**
 * 批量操作组合式函数
 * 提供批量删除、批量状态更新、批量排序等功能
 */
export function useBatchOperations() {
  // 响应式数据
  const batchLoading = ref(false);
  const sortLoading = ref(false);

  // 批量操作对话框
  const batchDialogVisible = ref(false);
  const batchOperation = ref<'delete' | 'enable' | 'disable' | 'sort'>('delete');

  // 排序数据
  const sortList = ref<Array<{ id: number; title: string; sortOrder: number }>>([]);

  /**
   * 批量删除
   */
  const handleBatchDelete = async (selectedIds: (number | string)[], refreshCallback: () => void) => {
    if (selectedIds.length === 0) {
      ElMessage.warning('请选择要删除的项目');
      return;
    }

    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.length} 个金刚位吗？`, '批量删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      });

      batchLoading.value = true;

      // 并发删除，但限制并发数量
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < selectedIds.length; i += batchSize) {
        const batch = selectedIds.slice(i, i + batchSize);
        batches.push(batch);
      }

      let successCount = 0;
      let failCount = 0;

      for (const batch of batches) {
        const promises = batch.map(async (id) => {
          try {
            const response = await delGridItem(Number(id));
            if (response.code === 200) {
              successCount++;
            } else {
              failCount++;
              console.error(`删除ID ${id} 失败:`, response.msg);
            }
          } catch (error) {
            failCount++;
            console.error(`删除ID ${id} 异常:`, error);
          }
        });

        await Promise.all(promises);
      }

      // 显示结果
      if (failCount === 0) {
        ElMessage.success(`批量删除成功，共删除 ${successCount} 个项目`);
      } else {
        ElNotification({
          title: '批量删除完成',
          message: `成功删除 ${successCount} 个，失败 ${failCount} 个`,
          type: successCount > 0 ? 'warning' : 'error',
          duration: 5000
        });
      }

      // 刷新列表
      refreshCallback();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除失败:', error);
        ElMessage.error('批量删除失败，请稍后重试');
      }
    } finally {
      batchLoading.value = false;
    }
  };

  /**
   * 批量启用
   */
  const handleBatchEnable = async (selectedRows: GridItemVO[], refreshCallback: () => void) => {
    if (selectedRows.length === 0) {
      ElMessage.warning('请选择要启用的项目');
      return;
    }

    const disabledItems = selectedRows.filter((row) => row.isActive === '1');
    if (disabledItems.length === 0) {
      ElMessage.info('选中的项目都已是启用状态');
      return;
    }

    try {
      await ElMessageBox.confirm(`确定要启用选中的 ${disabledItems.length} 个金刚位吗？`, '批量启用确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      });

      await batchUpdateStatus(disabledItems, '0', refreshCallback);
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量启用失败:', error);
        ElMessage.error('批量启用失败，请稍后重试');
      }
    }
  };

  /**
   * 批量禁用
   */
  const handleBatchDisable = async (selectedRows: GridItemVO[], refreshCallback: () => void) => {
    if (selectedRows.length === 0) {
      ElMessage.warning('请选择要禁用的项目');
      return;
    }

    const enabledItems = selectedRows.filter((row) => row.isActive === '0');
    if (enabledItems.length === 0) {
      ElMessage.info('选中的项目都已是禁用状态');
      return;
    }

    try {
      await ElMessageBox.confirm(`确定要禁用选中的 ${enabledItems.length} 个金刚位吗？`, '批量禁用确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });

      await batchUpdateStatus(enabledItems, '1', refreshCallback);
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量禁用失败:', error);
        ElMessage.error('批量禁用失败，请稍后重试');
      }
    }
  };

  /**
   * 批量更新状态
   */
  const batchUpdateStatus = async (items: GridItemVO[], status: string, refreshCallback: () => void) => {
    batchLoading.value = true;

    try {
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        batches.push(batch);
      }

      let successCount = 0;
      let failCount = 0;

      for (const batch of batches) {
        const promises = batch.map(async (item) => {
          try {
            const response = await updateGridItem({
              id: Number(item.id),
              isActive: status
            } as GridItemForm);

            if (response.code === 200) {
              successCount++;
            } else {
              failCount++;
              console.error(`更新ID ${item.id} 状态失败:`, response.msg);
            }
          } catch (error) {
            failCount++;
            console.error(`更新ID ${item.id} 状态异常:`, error);
          }
        });

        await Promise.all(promises);
      }

      // 显示结果
      const operation = status === '0' ? '启用' : '禁用';
      if (failCount === 0) {
        ElMessage.success(`批量${operation}成功，共${operation} ${successCount} 个项目`);
      } else {
        ElNotification({
          title: `批量${operation}完成`,
          message: `成功${operation} ${successCount} 个，失败 ${failCount} 个`,
          type: successCount > 0 ? 'warning' : 'error',
          duration: 5000
        });
      }

      // 刷新列表
      refreshCallback();
    } finally {
      batchLoading.value = false;
    }
  };

  /**
   * 打开批量排序对话框
   */
  const openBatchSort = (selectedRows: GridItemVO[]) => {
    if (selectedRows.length === 0) {
      ElMessage.warning('请选择要排序的项目');
      return;
    }

    if (selectedRows.length < 2) {
      ElMessage.warning('至少选择2个项目才能进行排序');
      return;
    }

    // 初始化排序列表
    sortList.value = selectedRows
      .map((row) => ({
        id: Number(row.id),
        title: row.title,
        sortOrder: row.sortOrder || 0
      }))
      .sort((a, b) => a.sortOrder - b.sortOrder);

    batchOperation.value = 'sort';
    batchDialogVisible.value = true;
  };

  /**
   * 移动排序项目
   */
  const moveSortItem = (index: number, direction: 'up' | 'down') => {
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    if (newIndex < 0 || newIndex >= sortList.value.length) {
      return;
    }

    // 交换位置
    const temp = sortList.value[index];
    sortList.value[index] = sortList.value[newIndex];
    sortList.value[newIndex] = temp;

    // 更新排序值
    sortList.value.forEach((item, idx) => {
      item.sortOrder = idx + 1;
    });
  };

  /**
   * 提交批量排序
   */
  const submitBatchSort = async (refreshCallback: () => void) => {
    try {
      sortLoading.value = true;

      const sortRequests: BatchSortRequest = {
        sortData: sortList.value.map((item) => ({
          id: Number(item.id),
          sortOrder: item.sortOrder
        }))
      };

      const response = await batchUpdateGridItemSort(sortRequests);

      if (response.code === 200) {
        ElMessage.success('批量排序成功');
        batchDialogVisible.value = false;
        refreshCallback();
      } else {
        ElMessage.error(response.msg || '批量排序失败');
      }
    } catch (error) {
      console.error('批量排序失败:', error);
      ElMessage.error('批量排序失败，请稍后重试');
    } finally {
      sortLoading.value = false;
    }
  };

  /**
   * 重置排序
   */
  const resetSort = () => {
    sortList.value.forEach((item, index) => {
      item.sortOrder = index + 1;
    });
  };

  /**
   * 关闭批量操作对话框
   */
  const closeBatchDialog = () => {
    batchDialogVisible.value = false;
    sortList.value = [];
  };

  // 计算属性
  const canMoveUp = computed(() => {
    return (index: number) => index > 0;
  });

  const canMoveDown = computed(() => {
    return (index: number) => index < sortList.value.length - 1;
  });

  const hasSelectedItems = computed(() => {
    return (selectedCount: number) => selectedCount > 0;
  });

  return {
    // 响应式数据
    batchLoading,
    sortLoading,
    batchDialogVisible,
    batchOperation,
    sortList,

    // 计算属性
    canMoveUp,
    canMoveDown,
    hasSelectedItems,

    // 方法
    handleBatchDelete,
    handleBatchEnable,
    handleBatchDisable,
    openBatchSort,
    moveSortItem,
    submitBatchSort,
    resetSort,
    closeBatchDialog
  };
}
