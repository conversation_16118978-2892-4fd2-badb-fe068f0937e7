import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  GridItemVO,
  GridItemForm,
  GridItemQuery,
  GridGroupVO,
  GridGroupForm,
  GridGroupQuery,
  GridPreviewVO,
  BatchSortRequest,
  CopyItemRequest,
  TableDataInfo
} from '@/api/otoconfig/gridconfig/types';

// ==================== 金刚位项目相关接口 ====================

// 金刚位分组相关API
export const gridGroupApi = {
  // 查询金刚位分组列表
  list: (params: GridGroupQuery): AxiosPromise<TableDataInfo<GridGroupVO>> => {
    return request({
      url: '/front/config/gridGroup/list',
      method: 'get',
      params
    });
  },

  // 获取金刚位分组详情
  getInfo: (id: number): AxiosPromise<GridGroupVO> => {
    return request({
      url: `/front/config/gridGroup/${id}`,
      method: 'get'
    });
  },

  // 新增金刚位分组
  add: (data: GridGroupForm): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridGroup',
      method: 'post',
      data
    });
  },

  // 修改金刚位分组
  update: (data: GridGroupForm): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridGroup',
      method: 'put',
      data
    });
  },

  // 删除金刚位分组
  remove: (ids: number[]): AxiosPromise<void> => {
    return request({
      url: `/front/config/gridGroup/${ids.join(',')}`,
      method: 'delete'
    });
  },

  // 导出金刚位分组
  export: (params: GridGroupQuery): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridGroup/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    });
  },

  // 根据平台查询分组列表
  getByPlatform: (platform: string): AxiosPromise<GridGroupVO[]> => {
    return request({
      url: `/front/config/gridGroup/platform/${platform}`,
      method: 'get'
    });
  },

  // 根据类型查询分组列表
  getByType: (type: string): AxiosPromise<GridGroupVO[]> => {
    return request({
      url: `/front/config/gridGroup/type/${type}`,
      method: 'get'
    });
  },

  // 查询默认分组
  getDefaultGroup: (platform: string): AxiosPromise<GridGroupVO> => {
    return request({
      url: '/front/config/gridGroup/default',
      method: 'get',
      params: { platform }
    });
  },

  // 批量更新排序值
  batchUpdateSort: (data: { id: number; sortOrder: number }[]): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridGroup/sort',
      method: 'put',
      data
    });
  },

  // 启用/禁用分组
  updateStatus: (id: number, isActive: number): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridGroup/status',
      method: 'put',
      params: { id, isActive }
    });
  },

  // 复制分组配置
  copy: (id: number, name: string): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridGroup/copy',
      method: 'post',
      params: { id, name }
    });
  },

  // 检查分组名称是否唯一
  checkNameUnique: (name: string, platform: string, excludeId?: number): AxiosPromise<boolean> => {
    return request({
      url: '/front/config/gridGroup/checkName',
      method: 'get',
      params: { name, platform, excludeId }
    });
  }
};

// 金刚位预览相关API
export const gridPreviewApi = {
  // 根据平台获取金刚位预览数据
  getByPlatform: (platform: string): AxiosPromise<GridPreviewVO> => {
    return request({
      url: `/front/config/gridPreview/platform/${platform}`,
      method: 'get'
    });
  },

  // 根据分组获取金刚位预览数据
  getByGroup: (groupId: number): AxiosPromise<GridPreviewVO> => {
    return request({
      url: `/front/config/gridPreview/group/${groupId}`,
      method: 'get'
    });
  }
};

// 金刚位项目相关API
export const gridItemApi = {
  // 查询金刚位项目列表
  list: (params: GridItemQuery): AxiosPromise<TableDataInfo<GridItemVO>> => {
    return request({
      url: '/front/config/gridItem/list',
      method: 'get',
      params
    });
  },

  // 获取金刚位项目详情
  getInfo: (id: number): AxiosPromise<GridItemVO> => {
    return request({
      url: `/front/config/gridItem/${id}`,
      method: 'get'
    });
  },

  // 新增金刚位项目
  add: (data: GridItemForm): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridItem',
      method: 'post',
      data
    });
  },

  // 修改金刚位项目
  update: (data: GridItemForm): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridItem',
      method: 'put',
      data
    });
  },

  // 删除金刚位项目
  remove: (ids: number[]): AxiosPromise<void> => {
    return request({
      url: `/front/config/gridItem/${ids.join(',')}`,
      method: 'delete'
    });
  },

  // 导出金刚位项目
  export: (params: GridItemQuery): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridItem/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    });
  },

  // 根据分组ID查询金刚位列表
  getByGroupId: (groupId: number): AxiosPromise<GridItemVO[]> => {
    return request({
      url: `/front/config/gridItem/group/${groupId}`,
      method: 'get'
    });
  },

  // 根据平台查询启用的金刚位列表
  getActiveByPlatform: (platform: string): AxiosPromise<GridItemVO[]> => {
    return request({
      url: `/front/config/gridItem/platform/${platform}`,
      method: 'get'
    });
  },

  // 根据分类查询金刚位列表
  getByCategory: (category: string): AxiosPromise<GridItemVO[]> => {
    return request({
      url: `/front/config/gridItem/category/${category}`,
      method: 'get'
    });
  },

  // 查询热门金刚位列表
  getHotItems: (platform: string, limit: number = 10): AxiosPromise<GridItemVO[]> => {
    return request({
      url: '/front/config/gridItem/hot',
      method: 'get',
      params: { platform, limit }
    });
  },

  // 查询新品金刚位列表
  getNewItems: (platform: string, limit: number = 10): AxiosPromise<GridItemVO[]> => {
    return request({
      url: '/front/config/gridItem/new',
      method: 'get',
      params: { platform, limit }
    });
  },

  // 批量更新排序值
  batchUpdateSort: (data: { id: number; sortOrder: number }[]): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridItem/sort',
      method: 'put',
      data
    });
  },

  // 启用/禁用金刚位
  updateStatus: (id: number, isActive: number): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridItem/status',
      method: 'put',
      params: { id, isActive }
    });
  },

  // 复制金刚位配置
  copy: (id: number, title: string): AxiosPromise<void> => {
    return request({
      url: '/front/config/gridItem/copy',
      method: 'post',
      params: { id, title }
    });
  },

  // 检查标题是否唯一
  checkTitleUnique: (title: string, platform: string, excludeId?: number): AxiosPromise<boolean> => {
    return request({
      url: '/front/config/gridItem/checkTitle',
      method: 'get',
      params: { title, platform, excludeId }
    });
  }
};

/**
 * 查询金刚位项目列表
 * @param query 查询参数
 */
export const listGridItem = (query?: GridItemQuery): AxiosPromise<GridItemVO[]> => {
  return request({
    url: '/front/config/gridItem/list',
    method: 'get',
    params: query
  });
};

/**
 * 导出金刚位项目列表
 * @param query 查询参数
 */
export const exportGridItem = (query?: GridItemQuery) => {
  return request({
    url: '/front/config/gridItem/export',
    method: 'post',
    data: query
  });
};

/**
 * 查询金刚位项目详细
 * @param id 项目ID
 */
export const getGridItem = (id: string | number): AxiosPromise<GridItemVO> => {
  return request({
    url: '/front/config/gridItem/' + id,
    method: 'get'
  });
};

/**
 * 新增金刚位项目
 * @param data 项目数据
 */
export const addGridItem = (data: GridItemForm) => {
  return request({
    url: '/front/config/gridItem',
    method: 'post',
    data: data
  });
};

/**
 * 修改金刚位项目
 * @param data 项目数据
 */
export const updateGridItem = (data: GridItemForm) => {
  return request({
    url: '/front/config/gridItem',
    method: 'put',
    data: data
  });
};

/**
 * 删除金刚位项目
 * @param id 项目ID
 */
export const delGridItem = (id: string | number | Array<string | number>) => {
  return request({
    url: '/front/config/gridItem/' + id,
    method: 'delete'
  });
};

/**
 * 根据分组ID查询金刚位项目
 * @param groupId 分组ID
 */
export const getGridItemsByGroupId = (groupId: string | number): AxiosPromise<GridItemVO[]> => {
  return request({
    url: '/front/config/gridItem/group/' + groupId,
    method: 'get'
  });
};

/**
 * 根据平台查询金刚位项目
 * @param platform 平台
 */
export const getGridItemsByPlatform = (platform: string): AxiosPromise<GridItemVO[]> => {
  return request({
    url: '/front/config/gridItem/platform/' + platform,
    method: 'get'
  });
};

/**
 * 根据分类查询金刚位项目
 * @param category 分类
 */
export const getGridItemsByCategory = (category: string): AxiosPromise<GridItemVO[]> => {
  return request({
    url: '/front/config/gridItem/category/' + category,
    method: 'get'
  });
};

/**
 * 获取热门金刚位项目
 * @param platform 平台
 */
export const getHotGridItems = (platform?: string): AxiosPromise<GridItemVO[]> => {
  return request({
    url: '/front/config/gridItem/hot',
    method: 'get',
    params: { platform }
  });
};

/**
 * 获取新品金刚位项目
 * @param platform 平台
 */
export const getNewGridItems = (platform?: string): AxiosPromise<GridItemVO[]> => {
  return request({
    url: '/front/config/gridItem/new',
    method: 'get',
    params: { platform }
  });
};

/**
 * 批量更新金刚位项目排序
 * @param data 排序数据
 */
export const batchUpdateGridItemSort = (data: BatchSortRequest) => {
  return request({
    url: '/front/config/gridItem/batchSort',
    method: 'put',
    data: data
  });
};

/**
 * 更新金刚位项目状态
 * @param id 项目ID
 * @param status 状态
 */
export const updateGridItemStatus = (id: string | number, status: string) => {
  return request({
    url: '/front/config/gridItem/status',
    method: 'put',
    params: {
      id: id,
      isActive: status
    }
  });
};

/**
 * 复制金刚位项目
 * @param data 复制参数
 */
export const copyGridItem = (data: CopyItemRequest) => {
  return request({
    url: '/front/config/gridItem/copy',
    method: 'post',
    data: data
  });
};

/**
 * 检查金刚位项目标题唯一性
 * @param title 标题
 * @param id 项目ID（编辑时传入）
 */
export const checkGridItemTitleUnique = (title: string, id?: string | number) => {
  return request({
    url: '/front/config/gridItem/checkTitleUnique',
    method: 'get',
    params: { title, id }
  });
};

// ==================== 金刚位分组相关接口 ====================

/**
 * 查询金刚位分组列表
 * @param query 查询参数
 */
export const listGridGroup = (query?: GridGroupQuery): AxiosPromise<TableDataInfo<GridGroupVO>> => {
  return request({
    url: '/front/config/gridGroup/list',
    method: 'get',
    params: query
  });
};

/**
 * 导出金刚位分组列表
 * @param query 查询参数
 */
export const exportGridGroup = (query?: GridGroupQuery) => {
  return request({
    url: '/front/config/gridGroup/export',
    method: 'post',
    data: query
  });
};

/**
 * 查询金刚位分组详细
 * @param id 分组ID
 */
export const getGridGroup = (id: string | number): AxiosPromise<GridGroupVO> => {
  return request({
    url: '/front/config/gridGroup/' + id,
    method: 'get'
  });
};

/**
 * 新增金刚位分组
 * @param data 分组数据
 */
export const addGridGroup = (data: GridGroupForm) => {
  return request({
    url: '/front/config/gridGroup',
    method: 'post',
    data: data
  });
};

/**
 * 修改金刚位分组
 * @param data 分组数据
 */
export const updateGridGroup = (data: GridGroupForm) => {
  return request({
    url: '/front/config/gridGroup',
    method: 'put',
    data: data
  });
};

/**
 * 删除金刚位分组
 * @param id 分组ID
 */
export const delGridGroup = (id: string | number | Array<string | number>) => {
  return request({
    url: '/front/config/gridGroup/' + id,
    method: 'delete'
  });
};

/**
 * 根据平台查询金刚位分组
 * @param platform 平台
 */
export const getGridGroupsByPlatform = (platform: string): AxiosPromise<GridGroupVO[]> => {
  return request({
    url: '/front/config/gridGroup/platform/' + platform,
    method: 'get'
  });
};

/**
 * 根据分组类型查询金刚位分组
 * @param groupType 分组类型
 */
export const getGridGroupsByType = (groupType: string): AxiosPromise<GridGroupVO[]> => {
  return request({
    url: '/front/config/gridGroup/type/' + groupType,
    method: 'get'
  });
};

/**
 * 获取默认金刚位分组
 * @param platform 平台
 */
export const getDefaultGridGroups = (platform: string): AxiosPromise<GridGroupVO[]> => {
  return request({
    url: '/front/config/gridGroup/default/' + platform,
    method: 'get'
  });
};

/**
 * 批量更新金刚位分组排序
 * @param data 排序数据
 */
export const batchUpdateGridGroupSort = (data: BatchSortRequest) => {
  return request({
    url: '/front/config/gridGroup/batchSort',
    method: 'put',
    data: data
  });
};

/**
 * 更新金刚位分组状态
 * @param id 分组ID
 * @param status 状态
 */
export const updateGridGroupStatus = (id: string | number, status: string) => {
  // 将字符串状态转换为后端期望的Integer类型
  const isActive = status === '0' ? 1 : 0;
  return request({
    url: '/front/config/gridGroup/status',
    method: 'put',
    params: {
      id: id,
      isActive: isActive
    }
  });
};

/**
 * 复制金刚位分组
 * @param sourceId 源分组ID
 * @param newName 新分组名称
 */
export const copyGridGroup = (sourceId: string | number, newName: string) => {
  return request({
    url: '/front/config/gridGroup/copy',
    method: 'post',
    data: { sourceId, newName }
  });
};

/**
 * 检查金刚位分组名称唯一性
 * @param groupName 分组名称
 * @param id 分组ID（编辑时传入）
 */
export const checkGridGroupNameUnique = (groupName: string, id?: string | number) => {
  return request({
    url: '/front/config/gridGroup/checkNameUnique',
    method: 'get',
    params: { groupName, id }
  });
};

/**
 * 获取金刚位分组选项（用于下拉框）
 * @param platform 平台
 */
export const getGridGroupOptions = (platform?: string) => {
  return request({
    url: '/front/config/gridGroup/options',
    method: 'get',
    params: { platform }
  });
};

// ==================== 金刚位预览相关接口 ====================

/**
 * 根据平台获取金刚位预览数据
 * @param platform 平台
 */
export const getGridPreviewByPlatform = (platform: string): AxiosPromise<GridPreviewVO> => {
  return request({
    url: '/front/config/gridPreview/platform/' + platform,
    method: 'get'
  });
};

/**
 * 根据分组ID获取金刚位预览数据
 * @param groupId 分组ID
 */
export const getGridPreviewByGroupId = (groupId: string | number): AxiosPromise<GridPreviewVO> => {
  return request({
    url: '/front/config/gridPreview/group/' + groupId,
    method: 'get'
  });
};
