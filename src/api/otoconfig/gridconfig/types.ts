/**
 * 金刚位配置相关类型定义
 */

/**
 * 金刚位项目查询参数
 */
export interface GridItemQuery {
  /**
   * 分页参数
   */
pageNum: number;
pageSize: number;
  
  /**
   * 标题
   */
  title?: string;

  /**
   * 服务类型
   */
  serviceType?: string;

  /**
   * 分类
   */
  category?: string;

  /**
   * 是否热门
   */
  isHot?: string;

  /**
   * 是否新品
   */
  isNew?: string;

  /**
   * 是否激活
   */
  isActive?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 平台
   */
  platform?: string;

  /**
   * 分组ID
   */
  groupId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 金刚位项目视图对象
 */
export interface GridItemVO {
  /**
   * 基础实体字段
   */
  createBy?: any;
  createTime?: string;
  updateBy?: any;
  updateTime?: string;
  
  /**
   * 主键ID
   */
id: string | number;

  /**
   * 标题
   */
  title: string;

  /**
   * 图标
   */
  icon: string;

  /**
   * 图标类型
   */
  iconType: string;

  /**
   * 渐变色
   */
  gradient: string;

  /**
   * 路由
   */
  route: string;

  /**
   * 服务类型
   */
  serviceType: string;

  /**
   * 分类
   */
  category: string;

  /**
   * 是否热门
   */
  isHot: string;

  /**
   * 是否新品
   */
  isNew: string;

  /**
   * 是否激活
   */
  isActive: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 图标URL
   */
  iconUrl: string;

  /**
   * 跳转URL
   */
  jumpUrl: string;

  /**
   * 描述
   */
  description: string;

  /**
   * 排序
   */
  sortOrder: number;

  /**
   * 平台
   */
  platform: string;

  /**
   * 最小版本
   */
  versionMin: string;

  /**
   * 最大版本
   */
  versionMax: string;

  /**
   * 开始时间
   */
  startTime: string;

  /**
   * 结束时间
   */
  endTime: string;

  /**
   * 目标用户
   */
  targetUsers: string;

  /**
   * 额外配置
   */
  extraConfig: string;

  /**
   * 分组ID
   */
  groupId: string | number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 删除标志
   */
  delFlag: string;
}

/**
 * 金刚位项目表单对象
 */
export interface GridItemForm {
  /**
   * 基础实体字段
   */
  createBy?: any;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  
  /**
   * 主键ID
   */
id?: string | number;

  /**
   * 标题
   */
  title?: string;

  /**
   * 图标
   */
  icon?: string;

  /**
   * 图标类型
   */
  iconType?: string;

  /**
   * 渐变色
   */
  gradient?: string;

  /**
   * 路由
   */
  route?: string;

  /**
   * 服务类型
   */
  serviceType?: string;

  /**
   * 分类
   */
  category?: string;

  /**
   * 是否热门
   */
  isHot?: string;

  /**
   * 是否新品
   */
  isNew?: string;

  /**
   * 是否激活
   */
  isActive?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 图标URL
   */
  iconUrl?: string;

  /**
   * 跳转URL
   */
  jumpUrl?: string;

  /**
   * 排序
   */
  sortOrder?: number;

  /**
   * 平台
   */
  platform?: string;

  /**
   * 最小版本
   */
  versionMin?: string;

  /**
   * 最大版本
   */
  versionMax?: string;

  /**
   * 开始时间
   */
  startTime?: string;

  /**
   * 结束时间
   */
  endTime?: string;

  /**
   * 目标用户
   */
  targetUsers?: string;

  /**
   * 额外配置
   */
  extraConfig?: string;

  /**
   * 分组ID
   */
  groupId?: string | number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 描述
   */
  description?: string;
}

/**
 * 金刚位分组查询参数
 */
export interface GridGroupQuery {
  /**
   * 分页参数
   */
pageNum: number;
pageSize: number;
  
  /**
   * 分组名称
   */
  groupName?: string;

  /**
   * 平台
   */
  platform?: string;

  /**
   * 分组类型
   */
  groupType?: string;

  /**
   * 状态：0-启用，1-禁用
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 表格数据信息
 */
export interface TableDataInfo<T> {
  /** 总记录数 */
  total: number;
  /** 列表数据 */
  rows: T[];
  /** 消息状态码 */
  code: number;
  /** 消息内容 */
  msg: string;
}

/**
 * 金刚位分组视图对象
 */
export interface GridGroupVO {
  /**
   * 基础实体字段
   */
  createBy?: any;
  createDept?: any;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  
  /**
   * 分组ID
   */
  groupId: string | number;

  /**
   * 分组名称
   */
  groupName: string;

  /**
   * 描述
   */
  description: string;

  /**
   * 平台
   */
  platform: string;

  /**
   * 分组类型
   */
  groupType: string;

  /**
   * 是否激活
   */
  isActive: number;

  /**
   * 排序
   */
  sortOrder: number;

  /**
   * 最大项目数
   */
  maxItems: number;

  /**
   * 布局配置
   */
  layoutConfig: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 删除标志
   */
  delFlag: string;
}

/**
 * 金刚位分组表单对象
 */
export interface GridGroupForm {
  /**
   * 基础实体字段
   */
  createBy?: any;
  createDept?: any;
  createTime?: string;
  updateBy?: any;
  updateTime?: any;
  
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 分组名称
   */
  groupName?: string;

  /**
   * 描述
   */
  description?: string;

  /**
   * 平台
   */
  platform?: string;

  /**
   * 分组类型
   */
  groupType?: string;

  /**
   * 是否激活
   */
  isActive?: string;

  /**
   * 排序
   */
  sortOrder?: number;

  /**
   * 最大项目数
   */
  maxItems?: number;

  /**
   * 布局配置
   */
  layoutConfig?: string;

  /**
   * 备注
   */
  remark?: string;
}

/**
 * 金刚位预览视图对象
 */
export interface GridPreviewVO {
  /**
   * 平台标识
   */
  platform: string;

  /**
   * 平台名称
   */
  platformName: string;

  /**
   * 分组列表（包含每个分组的项目）
   */
  groups: GridPreviewGroupDetailVO[];

  /**
   * 总项目数量
   */
  totalItems: number;

  /**
   * 启用项目数量
   */
  activeItems: number;

  /**
   * 热门项目数量
   */
  hotItems: number;

  /**
   * 新品项目数量
   */
  newItems: number;

  /**
   * 预览生成时间
   */
  generateTime: string;

  /**
   * 是否实时预览
   */
  isRealtime: boolean;
}

/**
 * 金刚位预览分组详细数据（后端返回的格式）
 */
export interface GridPreviewGroupDetailVO {
  /**
   * 分组ID
   */
  groupId: number;

  /**
   * 分组名称
   */
  groupName: string;

  /**
   * 分组类型
   */
  groupType: string;

  /**
   * 分组排序
   */
  sortOrder: number;

  /**
   * 是否启用
   */
  isActive: number;

  /**
   * 分组下的项目列表
   */
  items: GridItemPreviewVO[];

  /**
   * 分组项目数量
   */
  itemCount: number;
}

/**
 * 金刚位项目预览数据
 */
export interface GridItemPreviewVO {
  /**
   * 项目ID
   */
  itemId: number;

  /**
   * 项目标题
   */
  title: string;

  /**
   * 图标
   */
  icon: string;

  /**
   * 图标类型
   */
  iconType: string;

  /**
   * 路由
   */
  route: string;

  /**
   * 服务类型
   */
  serviceType: string;

  /**
   * 操作类型
   */
  actionType: string;

  /**
   * 分类
   */
  category: string;

  /**
   * 排序值
   */
  sortOrder: number;

  /**
   * 是否热门
   */
  isHot: number;

  /**
   * 是否新品
   */
  isNew: number;

  /**
   * 是否启用
   */
  isActive: number;

  /**
   * 最低版本要求
   */
  versionMin: string;

  /**
   * 最高版本要求
   */
  versionMax: string;

  /**
   * 生效开始时间
   */
  startTime: string;

  /**
   * 生效结束时间
   */
  endTime: string;

  /**
   * 扩展配置
   */
  extraConfig: string;

  /**
   * 是否在有效期内
   */
  isValid: boolean;
}

/**
 * 金刚位预览分组数据
 */
export interface GridPreviewGroupVO {
  /**
   * 分组ID
   */
  groupId: string | number;

  /**
   * 分组名称
   */
  groupName: string;

  /**
   * 分组项目列表
   */
  items: GridItemVO[];
}

/**
 * 批量排序请求
 */
export interface BatchSortRequest {
  /**
   * 排序数据
   */
  sortData: Array<{
    id: string | number;
    sortOrder: number;
  }>;
}

/**
 * 复制项目请求
 */
export interface CopyItemRequest {
  /**
   * 源项目ID
   */
  sourceId: string | number;

  /**
   * 目标分组ID
   */
  targetGroupId?: string | number;

  /**
   * 新标题
   */
  newTitle?: string;
}
