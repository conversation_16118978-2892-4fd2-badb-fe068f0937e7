import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OtoconfigVO, OtoconfigForm, OtoconfigQuery } from '@/api/otoconfig/otoconfig/types';

/**
 * 查询oto参数配置列表
 * @param query
 * @returns {*}
 */

export const listOtoconfig = (query?: OtoconfigQuery): AxiosPromise<OtoconfigVO[]> => {
  return request({
    url: '/otoconfig/otoconfig/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询oto参数配置详细
 * @param configId
 */
export const getOtoconfig = (configId: string | number): AxiosPromise<OtoconfigVO> => {
  return request({
    url: '/otoconfig/otoconfig/' + configId,
    method: 'get'
  });
};

/**
 * 新增oto参数配置
 * @param data
 */
export const addOtoconfig = (data: OtoconfigForm) => {
  return request({
    url: '/otoconfig/otoconfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改oto参数配置
 * @param data
 */
export const updateOtoconfig = (data: OtoconfigForm) => {
  return request({
    url: '/otoconfig/otoconfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除oto参数配置
 * @param configId
 */
export const delOtoconfig = (configId: string | number | Array<string | number>) => {
  return request({
    url: '/otoconfig/otoconfig/' + configId,
    method: 'delete'
  });
};

/**
 * 刷新oto参数配置缓存
 */
export const refreshOtoconfigCache = () => {
  return request({
    url: '/otoconfig/otoconfig/refresh',
    method: 'delete'
  });
};
