import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UserRegisterInfoQuery, UserRegisterInfoVO } from './types';
import { parseStrEmpty } from '@/utils/ruoyi';

/**
 * 查询用户注册日志（合规留痕/风控专用）列表
 * @param query
 * @returns {*}
 */
export const listUserRegisterInfo = (query?: UserRegisterInfoQuery) => {
  return request<{
    rows: UserRegisterInfoVO[];
    total: number;
    code: number;
    msg: string;
  }>({
    url: '/oto-manage/userRegisterInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询用户注册日志（合规留痕/风控专用）详细
 * @param id
 * @returns {*}
 */
export const getUserRegisterInfo = (
  id: string | number
): AxiosPromise<{
  data: UserRegisterInfoVO;
}> => {
  return request({
    url: '/oto-manage/userRegisterInfo/' + parseStrEmpty(id),
    method: 'get'
  });
};
