<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form ref="queryFormRef" :model="queryParams" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题关键词"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
          @clear="handleQuery"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="分组" prop="groupId">
        <el-select v-model="queryParams.groupId" placeholder="请选择分组" clearable style="width: 180px" @change="handleQuery">
          <el-option v-for="option in groupOptions" :key="option.value" :label="option.label" :value="option.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="平台" prop="platform">
        <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 150px" @change="handleQuery">
          <el-option label="全部" value="" />
          <el-option label="全平台" value="ALL" />
          <el-option label="iOS" value="IOS" />
          <el-option label="Android" value="ANDROID" />
          <el-option label="H5" value="H5" />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="isActive">
        <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable style="width: 120px" @change="handleQuery">
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery" :loading="tableLoading"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['otoconfig:gridconfig:add']"> 新增 </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button type="success" plain icon="View" @click="openPreview()"> 预览 </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" :loading="exportLoading" v-hasPermi="['otoconfig:gridconfig:export']">
          导出
        </el-button>
      </el-col>

      <!-- 批量操作 -->
      <template v-if="hasSelectedItems">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            @click="handleBatchDelete()"
            :loading="batchLoading"
            v-hasPermi="['otoconfig:gridconfig:remove']"
          >
            批量删除
          </el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Check"
            @click="handleBatchEnable()"
            :loading="batchLoading"
            v-hasPermi="['otoconfig:gridconfig:edit']"
          >
            批量启用
          </el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Close"
            @click="handleBatchDisable()"
            :loading="batchLoading"
            v-hasPermi="['otoconfig:gridconfig:edit']"
          >
            批量禁用
          </el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button type="info" plain icon="Sort" @click="openBatchSort()" v-hasPermi="['otoconfig:gridconfig:edit']">
            批量排序
          </el-button>
        </el-col>
      </template>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="() => getGridList(false)" :columns="columns" />
    </el-row>

    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>金刚位配置列表</span>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        ref="tableRef"
        v-loading="tableLoading"
        :data="gridList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        row-key="id"
        stripe
        border
        height="calc(100vh - 350px)"
      >
        <el-table-column type="selection" width="50" align="center" :reserve-selection="true" />

        <el-table-column label="ID" prop="id" width="80" align="center" sortable="custom" />

        <el-table-column label="标题" prop="title" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="title-cell">
              <span class="title-text">{{ row.title }}</span>
              <el-tag v-if="row.isHot" type="danger" size="small" class="ml-2"> 热门 </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="图标" prop="icon" width="80" align="center">
          <template #default="{ row }">
            <div class="icon-cell">
              <el-image v-if="row.icon" :src="row.icon" :preview-src-list="[row.icon]" class="grid-icon" fit="cover" lazy>
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div v-else class="no-icon">
                <el-icon><Picture /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="链接" prop="link" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="link-cell">
              <el-link v-if="row.link" :href="row.link" target="_blank" type="primary" class="link-text">
                {{ row.link }}
              </el-link>
              <span v-else class="text-gray-400">-</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="分组" prop="groupName" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.groupName" type="info" size="small">
              {{ row.groupName }}
            </el-tag>
            <span v-else class="text-gray-400">未分组</span>
          </template>
        </el-table-column>

        <el-table-column label="平台" prop="platform" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getPlatformTagType(row.platform)" size="small">
              {{ getPlatformLabel(row.platform) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="排序" prop="sort" width="80" align="center" sortable="custom">
          <template #default="{ row }">
            <el-input-number
              v-model="row.sort"
              :min="0"
              :max="9999"
              size="small"
              controls-position="right"
              @change="(val) => handleSortChange(row, val)"
              style="width: 80px"
            />
          </template>
        </el-table-column>

        <el-table-column label="状态" prop="isActive" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              active-value="1"
              inactive-value="0"
              active-text="启用"
              inactive-text="禁用"
              @change="(val) => handleStatusChange(row, val)"
              v-hasPermi="['otoconfig:gridconfig:edit']"
            />
          </template>
        </el-table-column>

        <el-table-column label="创建时间" prop="createTime" width="160" align="center" sortable="custom">
          <template #default="{ row }">
            <span>{{ formatTime(row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button link type="primary" icon="View" @click="handlePreview(row)" size="small"> 预览 </el-button>

              <el-button link type="primary" icon="Edit" @click="handleUpdate(row)" v-hasPermi="['otoconfig:gridconfig:edit']" size="small">
                修改
              </el-button>

              <el-button link type="warning" icon="CopyDocument" @click="handleCopy(row)" v-hasPermi="['otoconfig:gridconfig:add']" size="small">
                复制
              </el-button>

              <el-button link type="danger" icon="Delete" @click="handleDelete(row)" v-hasPermi="['otoconfig:gridconfig:remove']" size="small">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handlePagination"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        background
        layout="total, sizes, prev, pager, next, jumper"
      />
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" class="dialog-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input v-model="formData.title" placeholder="请输入标题" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图标" prop="icon">
              <el-input v-model="formData.icon" placeholder="请输入图标URL" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="路由" prop="route">
              <el-input v-model="formData.route" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务类型" prop="serviceType">
              <el-input v-model="formData.serviceType" placeholder="请输入服务类型" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-input v-model="formData.category" placeholder="请输入分类" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分组" prop="groupId">
              <el-select v-model="formData.groupId" placeholder="请选择分组" style="width: 100%">
                <el-option v-for="option in groupOptions" :key="option.value" :label="option.label" :value="option.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="平台" prop="platform">
              <el-select v-model="formData.platform" placeholder="请选择平台" style="width: 100%">
                <el-option label="全平台" value="ALL" />
                <el-option label="iOS" value="IOS" />
                <el-option label="Android" value="ANDROID" />
                <el-option label="H5" value="H5" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="formData.sortOrder" :min="0" :max="9999" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="热门" prop="isHot">
              <el-switch v-model="formData.isHot" active-value="1" inactive-value="0" active-text="是" inactive-text="否" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="新品" prop="isNew">
              <el-switch v-model="formData.isNew" active-value="1" inactive-value="0" active-text="是" inactive-text="否" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="isActive">
              <el-switch v-model="formData.isActive" active-value="1" inactive-value="0" active-text="启用" inactive-text="禁用" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelForm">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GridConfig" lang="ts">
import { ref, reactive, toRefs, computed, onMounted, getCurrentInstance, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Picture, View, Edit, CopyDocument, Delete, Plus, Check, Close, Sort } from '@element-plus/icons-vue';

// API 和类型
import {
  gridItemApi,
  getGridItem,
  delGridItem,
  addGridItem,
  updateGridItem,
  updateGridItemStatus,
  copyGridItem,
  gridGroupApi
} from '@/api/otoconfig/gridconfig';
import { GridItemVO, GridItemForm, GridItemQuery, GridGroupVO, GridGroupQuery, TableDataInfo } from '@/api/otoconfig/gridconfig/types';

const { proxy } = getCurrentInstance() as any;
const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

// 响应式数据
const gridList = ref<GridItemVO[]>([]);
const groupOptions = ref<Array<{ label: string; value: string | number }>>([]);
const showSearch = ref(true);
const tableLoading = ref(false);
const exportLoading = ref(false);
const batchLoading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const selectedIds = ref<number[]>([]);
const selectedRows = ref<GridItemVO[]>([]);
const total = ref(0);

const queryFormRef = ref();
const tableRef = ref();
const formRef = ref();

// 查询参数
const queryParams = ref<GridItemQuery>({
  pageNum: 1,
  pageSize: 10,
  title: '',
  groupId: undefined,
  platform: '',
  isActive: ''
});

// 表单数据
const formData = ref<GridItemForm>({
  id: undefined,
  title: '',
  icon: '',
  route: '',
  serviceType: '',
  category: '',
  isHot: '0',
  isNew: '0',
  isActive: '1',
  sortOrder: 0,
  platform: '',
  groupId: undefined
});

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 1, max: 50, message: '标题长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  groupId: [{ required: true, message: '请选择分组', trigger: 'change' }],
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  sortOrder: [{ required: true, message: '请输入排序值', trigger: 'blur' }]
};

// 计算属性
const hasSelectedItems = computed(() => selectedIds.value.length > 0);

const columns = ref([
  { key: 'id', label: 'ID', visible: true },
  { key: 'title', label: '标题', visible: true },
  { key: 'icon', label: '图标', visible: true },
  { key: 'route', label: '路由', visible: true },
  { key: 'groupId', label: '分组', visible: true },
  { key: 'platform', label: '平台', visible: true },
  { key: 'sortOrder', label: '排序', visible: true },
  { key: 'isActive', label: '状态', visible: true },
  { key: 'createTime', label: '创建时间', visible: true }
]);

// 方法
const getGridList = async (showLoading = true) => {
  if (showLoading) tableLoading.value = true;
  isDataLoading.value = true;
  try {
    const res = await gridItemApi.list(queryParams.value);
    gridList.value = res.data.rows;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取金刚位列表失败:', error);
  } finally {
    if (showLoading) tableLoading.value = false;
    // 延迟重置标志，确保Vue完成数据绑定
    nextTick(() => {
      isDataLoading.value = false;
    });
  }
};

const getGroupOptions = async () => {
  try {
    const res = await gridGroupApi.list({
      pageNum: 1,
      pageSize: 100
    } as GridGroupQuery);
    groupOptions.value = res.data.rows.map((item: GridGroupVO) => ({
      label: item.groupName,
      value: item.id
    }));
  } catch (error) {
    console.error('获取分组选项失败:', error);
  }
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getGridList();
};

const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

const handleSelectionChange = (selection: GridItemVO[]) => {
  selectedIds.value = selection.map((item) => Number(item.id));
  selectedRows.value = selection;
};

const handlePagination = () => {
  getGridList();
};

const handleAdd = () => {
  formData.value = {
    id: undefined,
    title: '',
    icon: '',
    route: '',
    serviceType: '',
    category: '',
    isHot: '0',
    isNew: '0',
    isActive: '1',
    sortOrder: 0,
    platform: '',
    groupId: undefined
  };
  dialogVisible.value = true;
  dialogTitle.value = '新增金刚位配置';
};

const handleUpdate = async (row: GridItemVO) => {
  try {
    const res = await getGridItem(row.id);
    formData.value = res.data;
    dialogVisible.value = true;
    dialogTitle.value = '修改金刚位配置';
  } catch (error) {
    console.error('获取金刚位详情失败:', error);
  }
};

const handleDelete = async (row: GridItemVO) => {
  try {
    await ElMessageBox.confirm(`是否确认删除金刚位配置"${row.title}"？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    await delGridItem(row.id);
    ElMessage.success('删除成功');
    getGridList(false);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
    }
  }
};

const handleCopy = async (row: GridItemVO) => {
  try {
    await copyGridItem({ sourceId: row.id, newTitle: `${row.title}_副本` });
    ElMessage.success('复制成功');
    getGridList(false);
  } catch (error) {
    console.error('复制失败:', error);
  }
};

const handleExport = async () => {
  exportLoading.value = true;
  try {
    proxy?.download(
      'otoconfig/gridconfig/export',
      {
        ...queryParams.value
      },
      `gridconfig_${new Date().getTime()}.xlsx`
    );
  } catch (error) {
    console.error('导出失败:', error);
  } finally {
    exportLoading.value = false;
  }
};

// 添加一个标志来跟踪数据是否正在初始化
const isDataLoading = ref(false);

const handleStatusChange = async (row: GridItemVO, value: string | number | boolean) => {
  // 如果数据正在加载中，不触发状态变更
  if (isDataLoading.value) {
    return;
  }

  const stringValue = String(value);
  const text = stringValue === '1' ? '启用' : '禁用';
  try {
    await ElMessageBox.confirm(`确认要${text}"${row.title}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    row.isActive = stringValue;
    await updateGridItemStatus(row.id, stringValue);
    ElMessage.success(`${text}成功`);
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${text}失败:`, error);
      // 恢复原状态
      row.isActive = stringValue === '1' ? '0' : '1';
    }
  }
};

const handleSortChange = async (row: GridItemVO, value: number) => {
  try {
    row.sortOrder = value;
    await updateGridItem(row);
    ElMessage.success('排序更新成功');
  } catch (error) {
    console.error('排序更新失败:', error);
  }
};

// 批量操作方法
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  try {
    await ElMessageBox.confirm(`确认删除选中的 ${selectedIds.value.length} 条数据？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    batchLoading.value = true;
    for (const id of selectedIds.value) {
      await delGridItem(id);
    }
    ElMessage.success('批量删除成功');
    getGridList(false);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
    }
  } finally {
    batchLoading.value = false;
  }
};

const handleBatchEnable = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要启用的数据');
    return;
  }

  try {
    batchLoading.value = true;
    const promises = selectedRows.value.map((row) => {
      row.isActive = '1';
      return updateGridItem(row);
    });
    await Promise.all(promises);
    ElMessage.success('批量启用成功');
    getGridList(false);
  } catch (error) {
    console.error('批量启用失败:', error);
  } finally {
    batchLoading.value = false;
  }
};

const handleBatchDisable = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要禁用的数据');
    return;
  }

  try {
    batchLoading.value = true;
    const promises = selectedRows.value.map((row) => {
      row.isActive = '0';
      return updateGridItem(row);
    });
    await Promise.all(promises);
    ElMessage.success('批量禁用成功');
    getGridList(false);
  } catch (error) {
    console.error('批量禁用失败:', error);
  } finally {
    batchLoading.value = false;
  }
};

const openBatchSort = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要排序的数据');
    return;
  }
  ElMessage.info('批量排序功能开发中...');
};

const openPreview = () => {
  ElMessage.info('预览功能开发中...');
};

// 工具方法
const formatTime = (time: string) => {
  if (!time) return '-';
  return proxy.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');
};

const getPlatformLabel = (platform: string) => {
  const platformMap: Record<string, string> = {
    'ALL': '全平台',
    'IOS': 'iOS',
    'ANDROID': 'Android',
    'H5': 'H5'
  };
  return platformMap[platform] || platform;
};

const getPlatformTagType = (platform: string): 'success' | 'primary' | 'warning' | 'info' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info'> = {
    'ALL': 'success',
    'IOS': 'primary',
    'ANDROID': 'warning',
    'H5': 'info'
  };
  return typeMap[platform] || 'info';
};

// 表单提交和取消
const submitForm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (formData.value.id) {
      await updateGridItem(formData.value);
      ElMessage.success('修改成功');
    } else {
      await addGridItem(formData.value);
      ElMessage.success('新增成功');
    }

    dialogVisible.value = false;
    getGridList(false);
  } catch (error) {
    console.error('表单提交失败:', error);
  }
};

const cancelForm = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 预览单个项目
const handlePreview = (row: GridItemVO) => {
  ElMessage.info(`预览功能开发中... 项目: ${row.title}`);
};

// 生命周期
onMounted(() => {
  getGridList();
  getGroupOptions();
});
</script>
