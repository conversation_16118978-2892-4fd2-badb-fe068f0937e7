<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="项目标题" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入项目标题" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="平台" prop="platform">
              <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable>
                <el-option label="APP" value="APP" />
                <el-option label="H5" value="H5" />
                <el-option label="小程序" value="MINI_PROGRAM" />
              </el-select>
            </el-form-item>
            <el-form-item label="分类" prop="category">
              <el-select v-model="queryParams.category" placeholder="请选择分类" clearable>
                <el-option label="热门" value="HOT" />
                <el-option label="新品" value="NEW" />
                <el-option label="推荐" value="RECOMMEND" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option label="启用" value="1" />
                <el-option label="禁用" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['otoconfig:gridItem:add']"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['otoconfig:gridItem:edit']"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['otoconfig:gridItem:remove']"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['otoconfig:gridItem:export']">导出 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleBatchSort">批量排序</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="tableRef" v-loading="loading" border :data="gridItemList" @selection-change="handleSelectionChange" @row-click="handleRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="false" />
        <el-table-column label="项目标题" align="center" prop="title" />
        <el-table-column label="图标" align="center" prop="iconUrl" width="80">
          <template #default="scope">
            <el-image v-if="scope.row.iconUrl" :src="scope.row.iconUrl" style="width: 40px; height: 40px" fit="cover" />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="平台" align="center" prop="platform" />
        <el-table-column label="分类" align="center" prop="category" />
        <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['otoconfig:gridItem:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="复制" placement="top">
              <el-button link type="primary" icon="CopyDocument" @click="handleCopy(scope.row)" v-hasPermi="['otoconfig:gridItem:add']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['otoconfig:gridItem:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改金刚位项目对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="gridItemFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入项目标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台" prop="platform">
              <el-select v-model="form.platform" placeholder="请选择平台">
                <el-option label="APP" value="APP" />
                <el-option label="H5" value="H5" />
                <el-option label="小程序" value="MINI_PROGRAM" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="form.category" placeholder="请选择分类">
                <el-option label="热门" value="HOT" />
                <el-option label="新品" value="NEW" />
                <el-option label="推荐" value="RECOMMEND" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分组" prop="groupId">
              <el-select v-model="form.groupId" placeholder="请选择分组">
                <el-option v-for="group in groupOptions" :key="group.id" :label="group.groupName" :value="group.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图标URL" prop="iconUrl">
              <el-input v-model="form.iconUrl" placeholder="请输入图标URL" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="跳转URL" prop="jumpUrl">
              <el-input v-model="form.jumpUrl" placeholder="请输入跳转URL" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" placeholder="排序" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量排序对话框 -->
    <el-dialog title="批量排序" v-model="sortDialog.visible" width="400px" append-to-body>
      <el-form ref="sortFormRef" :model="sortForm" label-width="80px">
        <el-form-item label="排序方式">
          <el-radio-group v-model="sortForm.sortType">
            <el-radio label="asc">升序</el-radio>
            <el-radio label="desc">降序</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="起始值">
          <el-input-number v-model="sortForm.startValue" :min="0" placeholder="起始排序值" />
        </el-form-item>
        <el-form-item label="步长">
          <el-input-number v-model="sortForm.step" :min="1" placeholder="排序步长" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchSort">确 定</el-button>
          <el-button @click="sortDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GridItem" lang="ts">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue';
import type { ComponentInternalInstance } from 'vue';
import type { FormInstance } from 'element-plus';
import { listGridItem, getGridItem, delGridItem, addGridItem, updateGridItem, updateGridItemStatus, copyGridItem } from '@/api/otoconfig/gridconfig';
import { listGridGroup } from '@/api/otoconfig/gridconfig';
import { GridItemVO, GridItemQuery, GridItemForm, GridGroupVO } from '@/api/otoconfig/gridconfig/types';

interface PageData<T, U> {
  form: T;
  queryParams: U;
  rules: any;
}

interface DialogOption {
  visible: boolean;
  title: string;
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance & {
  proxy: {
    $modal: {
      confirm: (message: string) => Promise<void>;
      msgSuccess: (message: string) => void;
      msgError: (message: string) => void;
    };
  };
};

const gridItemList = ref<GridItemVO[]>([]);
const groupOptions = ref<GridGroupVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<FormInstance>();
const gridItemFormRef = ref<FormInstance>();
const sortFormRef = ref<FormInstance>();
const tableRef = ref();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const sortDialog = reactive({
  visible: false
});

const initFormData: GridItemForm = {
  id: undefined,
  title: undefined,
  iconUrl: undefined,
  jumpUrl: undefined,
  platform: undefined,
  category: undefined,
  groupId: undefined,
  sortOrder: 0,
  status: '1',
  description: undefined
};

const data = reactive<PageData<GridItemForm, GridItemQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    platform: undefined,
    category: undefined,
    groupId: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    title: [{ required: true, message: '项目标题不能为空', trigger: 'blur' }],
    platform: [{ required: true, message: '平台不能为空', trigger: 'change' }],
    category: [{ required: true, message: '分类不能为空', trigger: 'change' }],
    iconUrl: [{ required: true, message: '图标URL不能为空', trigger: 'blur' }],
    jumpUrl: [{ required: true, message: '跳转URL不能为空', trigger: 'blur' }]
  }
});

const sortForm = reactive({
  sortType: 'asc',
  startValue: 1,
  step: 1
});

const { queryParams, form, rules } = toRefs(data);

/** 查询金刚位项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGridItem(queryParams.value);
  gridItemList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 查询分组选项 */
const getGroupOptions = async () => {
  const res = await listGridGroup({ pageNum: 1, pageSize: 1000 });
  groupOptions.value = (res.rows || []) as GridGroupVO[];
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  gridItemFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: GridItemVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加金刚位项目';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: GridItemVO) => {
  reset();
  if (row) {
    tableRef.value?.clearSelection();
    tableRef.value?.toggleRowSelection(row, true);
    ids.value = [row.id];
    single.value = false;
    multiple.value = false;
  }
  const _id = row?.id || ids.value[0];
  if (_id == null || _id === 0) {
    proxy?.$modal.msgError('请选择要修改的项目');
    return;
  }
  const res = await getGridItem(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改金刚位项目';
};

/** 提交按钮 */
const submitForm = () => {
  gridItemFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGridItem(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addGridItem(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: GridItemVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除金刚位项目编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delGridItem(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'otoconfig/gridItem/export',
    {
      ...queryParams.value
    },
    `gridItem_${new Date().getTime()}.xlsx`
  );
};

/** 状态切换 */
const handleStatusChange = async (row: GridItemVO) => {
  try {
    await updateGridItemStatus(String(row.id), row.status);
    proxy?.$modal.msgSuccess('状态更新成功');
  } catch (error) {
    row.status = row.status === '1' ? '0' : '1'; // 回滚状态
    proxy?.$modal.msgError('状态更新失败');
  }
};

/** 复制按钮操作 */
const handleCopy = async (row: GridItemVO) => {
  await proxy?.$modal.confirm('是否确认复制该项目？');
  await copyGridItem({ id: String(row.id) } as any);
  proxy?.$modal.msgSuccess('复制成功');
  await getList();
};

/** 批量排序 */
const handleBatchSort = () => {
  proxy?.$modal.msgError('批量排序功能已移除');
};

/** 提交批量排序 */
const submitBatchSort = async () => {
  proxy?.$modal.msgError('批量排序功能已移除');
  sortDialog.visible = false;
};

const handleRowClick = (row: GridItemVO) => {
  tableRef.value?.toggleRowSelection(row);
};

onMounted(() => {
  getList();
  getGroupOptions();
});
</script>
