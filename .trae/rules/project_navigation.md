# Fit-Manage-UI 项目导航 - 企业管理后台系统

> **项目定位**：管理端后台应用 | **技术栈**：Vue3 + TypeScript + Element Plus + Vite  
> **业务范围**：多租户企业管理系统 | **架构角色**：管理控制层

---

## 🏗️ 项目架构概览

### 核心定位
- **项目类型**：企业级管理后台系统（基于RuoYi-Vue-Plus）
- **业务范围**：多租户管理、用户权限、系统配置、数据监控
- **技术特点**：现代化Vue3企业级前端框架
- **用户群体**：系统管理员、企业管理人员、运营人员

### 与其他项目的关系
- **依赖关系**：调用 `oto-home` 后端服务进行管理操作
- **管理范围**：管理 `oto-ui` 移动端应用的用户数据和业务配置
- **数据流向**：管理员操作 → 后端API → 影响前端业务数据
- **权限控制**：提供细粒度的权限管理和数据权限控制

---

## 📋 核心技术栈

### 前端框架
- **Vue 3.5.13** - 现代化Vue框架，支持Composition API
- **TypeScript** - 强类型语言支持，提高代码质量
- **Element Plus 2.9.8** - 企业级UI组件库
- **Vite** - 现代化构建工具，快速开发体验

### 状态管理与路由
- **Pinia 3.0.2** - Vue3官方推荐的状态管理库
- **Vue Router 4.5.0** - 官方路由管理器
- **Vue I18n 11.1.3** - 国际化支持

### 功能增强库
- **VXE Table 4.13.7** - 高性能表格组件
- **ECharts 5.6.0** - 数据可视化图表库
- **Axios 1.8.4** - HTTP客户端
- **Crypto-js 4.2.0** - 加密工具库

---

## 🗂️ 项目结构导航

### 核心目录结构
```
src/
├── api/                    # API接口层
│   ├── system/            # 系统管理接口
│   ├── monitor/           # 系统监控接口
│   └── tool/              # 工具类接口
├── assets/                # 静态资源
│   ├── icons/             # 图标资源
│   ├── images/            # 图片资源
│   └── styles/            # 样式文件
├── components/            # 公共组件
│   ├── Breadcrumb/        # 面包屑导航
│   ├── Pagination/        # 分页组件
│   └── RightToolbar/      # 右侧工具栏
├── directive/             # 自定义指令
├── hooks/                 # Composition API钩子
├── layout/                # 布局组件
│   ├── components/        # 布局子组件
│   └── index.vue          # 主布局
├── router/                # 路由配置
├── store/                 # Pinia状态管理
│   ├── modules/           # 状态模块
│   └── index.ts           # 状态入口
├── types/                 # TypeScript类型定义
├── utils/                 # 工具函数
│   ├── auth.ts            # 认证工具
│   ├── request.ts         # 请求封装
│   └── validate.ts        # 验证工具
└── views/                 # 页面视图
    ├── system/            # 系统管理
    ├── monitor/           # 系统监控
    ├── tool/              # 系统工具
    └── dashboard/         # 仪表盘
```

### 核心业务模块
- **系统管理** (`views/system/`)：用户、角色、菜单、部门、岗位管理
- **系统监控** (`views/monitor/`)：在线用户、定时任务、数据监控
- **系统工具** (`views/tool/`)：代码生成、系统接口、表单构建
- **仪表盘** (`views/dashboard/`)：数据概览、统计图表

---

## 🔧 核心功能特性

### 多租户管理
- **租户管理**：租户信息、套餐配置、过期管理
- **租户套餐**：菜单权限、功能模块、用户数量限制
- **数据隔离**：租户间数据完全隔离，确保数据安全

### 权限管理
- **用户管理**：用户信息、部门分配、角色授权
- **角色管理**：角色权限、数据权限、菜单权限
- **菜单管理**：菜单配置、按钮权限、操作权限
- **部门管理**：组织架构、数据权限范围

### 系统配置
- **字典管理**：系统字典、数据字典维护
- **参数管理**：系统参数、动态配置
- **文件管理**：文件上传、下载、存储配置
- **通知公告**：系统公告、消息通知

### 监控运维
- **操作日志**：用户操作记录、异常日志
- **登录日志**：登录记录、安全监控
- **在线用户**：在线用户监控、强制下线
- **服务监控**：系统性能、资源使用情况

### 开发工具
- **代码生成**：多数据源代码生成、CRUD模板
- **系统接口**：API文档、接口测试
- **表单构建**：可视化表单设计器
- **定时任务**：任务调度、执行监控

---

## 📚 开发规范

### 核心规则文档
- **[Element Plus组件规范](frontend/element-plus-standards.md)** - 组件使用、主题定制、自定义指令完整规范

### 代码规范
- **ESLint配置**：严格的代码质量检查
- **Prettier格式化**：统一的代码格式
- **TypeScript严格模式**：类型安全保障
- **组件化开发**：高度模块化的组件设计

### 命名规范
- **文件命名**：kebab-case（短横线命名）
- **组件命名**：PascalCase（大驼峰命名）
- **变量命名**：camelCase（小驼峰命名）
- **常量命名**：UPPER_SNAKE_CASE（大写下划线）

### 目录规范
- **按功能模块分组**：相关功能文件集中管理
- **分层架构**：API、业务逻辑、视图层清晰分离
- **公共资源集中**：组件、工具、样式统一管理

---

## ⚠️ 重要约束

### 开发约束
1. **严格遵循Vue3 Composition API**：优先使用组合式API进行开发
2. **TypeScript强制使用**：所有代码必须使用TypeScript
3. **Element Plus组件优先**：UI组件优先使用Element Plus
4. **响应式设计**：确保在不同屏幕尺寸下正常显示
5. **权限控制严格**：所有操作必须经过权限验证

### 安全约束
- **数据权限**：严格按照用户权限范围显示和操作数据
- **操作日志**：重要操作必须记录日志
- **输入验证**：所有用户输入必须进行验证和过滤
- **XSS防护**：防止跨站脚本攻击

### 性能约束
- **懒加载**：路由和组件实现懒加载
- **虚拟滚动**：大数据量表格使用虚拟滚动
- **缓存策略**：合理使用缓存减少请求
- **打包优化**：代码分割和压缩优化

---

## 🚀 快速开始

### 开发环境
```bash
# 安装依赖
npm install --registry=https://registry.npmmirror.com

# 启动开发服务
npm run dev

# 访问地址
http://localhost:80
```

### 构建部署
```bash
# 构建生产环境
npm run build:prod

# 构建开发环境
npm run build:dev

# 预览构建结果
npm run preview
```

### 代码检查
```bash
# ESLint检查
npm run lint:eslint

# ESLint修复
npm run lint:eslint:fix

# Prettier格式化
npm run prettier
```

---

## 🔗 快速链接

- **[返回项目总导航](../../.trae/rules/unified_standards.md)** - 查看所有项目的统一规范
- **[OTO-Home后端服务](../../../oto-home/.trae/rules/project_navigation.md)** - 后端API服务
- **[OTO-UI移动端应用](../../../oto-ui/.trae/rules/project_navigation.md)** - 移动端前台应用

### 相关文档
- **[RuoYi-Vue-Plus官方文档](https://plus-doc.dromara.org/)** - 框架官方文档
- **[Element Plus文档](https://element-plus.org/zh-CN/)** - UI组件库文档
- **[Vue3官方文档](https://v3.cn.vuejs.org/)** - Vue3框架文档
- **[TypeScript文档](https://www.typescriptlang.org/)** - TypeScript语言文档

---

*最后更新：2024年12月 | 维护团队：OTO项目组*