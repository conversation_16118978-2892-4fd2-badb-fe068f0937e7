# 金刚位配置管理系统 - 前端必需后端API接口文档

## 文档说明

本文档基于对现有金刚位配置前端代码的深入分析，识别出前端功能实际需要的后端API接口。通过代码分析，确定了哪些接口是前端必须实现的，哪些是未使用的。

## 前端代码分析结果

通过对以下前端文件的分析：
- `src/views/otoconfig/gridconfig/index.vue`
- `src/views/otoconfig/gridconfig/gridItem/index.vue`
- `src/views/otoconfig/gridconfig/gridGroup/index.vue`
- `src/views/otoconfig/gridpreview/index.vue`
- `src/hooks/useGridConfig.ts`
- `src/hooks/useGridPreview.ts`
- `src/hooks/useBatchOperations.ts`
- `src/store/modules/gridConfig.ts`

确定了前端实际使用的API接口。

## 前端必需的后端API接口

### 1. 金刚位项目管理接口 (GridItem)

#### 1.1 基础CRUD操作
```typescript
// 查询金刚位项目列表 - ✅ 必需
GET /front/config/gridItem/list

// 获取金刚位项目详情 - ✅ 必需
GET /front/config/gridItem/{id}

// 新增金刚位项目 - ✅ 必需
POST /front/config/gridItem

// 修改金刚位项目 - ✅ 必需
PUT /front/config/gridItem

// 删除金刚位项目 - ✅ 必需
DELETE /front/config/gridItem/{ids}
```

#### 1.2 状态管理
```typescript
// 更新项目状态 - ✅ 必需
PUT /front/config/gridItem/status/{id}/{status}
```

#### 1.3 复制功能
```typescript
// 复制金刚位项目 - ✅ 必需
POST /front/config/gridItem/copy
```

#### 1.4 批量操作
```typescript
// 批量更新排序 - ✅ 必需
PUT /front/config/gridItem/sort
```

### 2. 金刚位分组管理接口 (GridGroup)

#### 2.1 基础CRUD操作
```typescript
// 查询金刚位分组列表 - ✅ 必需
GET /front/config/gridGroup/list

// 获取金刚位分组详情 - ✅ 必需
GET /front/config/gridGroup/{id}

// 新增金刚位分组 - ✅ 必需
POST /front/config/gridGroup

// 修改金刚位分组 - ✅ 必需
PUT /front/config/gridGroup

// 删除金刚位分组 - ✅ 必需
DELETE /front/config/gridGroup/{ids}
```

#### 2.2 状态管理
```typescript
// 更新分组状态 - ✅ 必需
PUT /front/config/gridGroup/status/{id}/{status}
```

#### 2.3 复制功能
```typescript
// 复制金刚位分组 - ✅ 必需
POST /front/config/gridGroup/copy
```

#### 2.4 名称唯一性校验
```typescript
// 检查分组名称唯一性 - ✅ 必需
GET /front/config/gridGroup/checkNameUnique
```

### 3. 金刚位预览接口 (GridPreview)

#### 3.1 预览功能
```typescript
// 按平台预览 - ✅ 必需
GET /front/config/gridPreview/platform/{platform}
```

## 前端未使用的接口（已从原文档中删除）

通过代码分析，以下接口在前端代码中未被使用，因此从文档中删除：

### 删除的GridItem接口：
- `GET /front/config/gridItem/export` - 导出功能（前端使用下载工具实现）
- `GET /front/config/gridItem/group/{groupId}` - 按分组查询（前端通过列表查询实现）
- `GET /front/config/gridItem/platform/{platform}` - 按平台查询（前端通过列表查询实现）
- `GET /front/config/gridItem/category/{category}` - 按分类查询（前端未使用）
- `GET /front/config/gridItem/hot` - 热门项目查询（前端未使用）
- `GET /front/config/gridItem/new` - 新品项目查询（前端未使用）
- `GET /front/config/gridItem/checkTitleUnique` - 标题唯一性校验（前端未使用）

### 删除的GridGroup接口：
- `GET /front/config/gridGroup/export` - 导出功能（前端使用下载工具实现）
- `GET /front/config/gridGroup/platform/{platform}` - 按平台查询（前端未使用）
- `GET /front/config/gridGroup/type/{type}` - 按类型查询（前端未使用）
- `GET /front/config/gridGroup/default` - 默认分组查询（前端未使用）
- `PUT /front/config/gridGroup/sort` - 批量排序（前端未使用）
- `GET /front/config/gridGroup/options` - 分组选项（前端未使用）

### 删除的GridPreview接口：
- `GET /front/config/gridPreview/group/{groupId}` - 按分组预览（前端未使用）

## 总结

本文档基于对现有金刚位配置前端代码的分析，识别出了前端实际使用的后端API接口。这些接口是后端必须实现的核心功能，确保前端应用的正常运行。

### 分析方法：
1. 扫描前端代码中的API调用
2. 识别实际使用的接口方法
3. 删除未使用的接口定义
4. 保留前端必需的核心接口

### 建议：
后端开发团队应优先实现上述列出的接口，确保前端功能的完整性和稳定性。