<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金刚位分组配置演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .demo-header p {
            color: #666;
            font-size: 14px;
        }
        
        .platform-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: white;
            border-radius: 12px;
            padding: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .platform-tab {
            padding: 10px 20px;
            margin: 0 5px;
            border: none;
            background: #f0f0f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .platform-tab.active {
            background: #007AFF;
            color: white;
        }
        
        .platform-demo {
            display: none;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .platform-demo.active {
            display: block;
        }
        
        .group-container {
            margin-bottom: 30px;
            border: 2px dashed #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            background: #fafafa;
        }
        
        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .group-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .group-info {
            font-size: 12px;
            color: #666;
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .grid-container {
            display: grid;
            gap: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
        }
        
        .grid-4 {
            grid-template-columns: repeat(4, 1fr);
        }
        
        .grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .grid-5 {
            grid-template-columns: repeat(5, 1fr);
        }
        
        .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 10px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .grid-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .grid-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 8px;
        }
        
        .grid-title {
            font-size: 12px;
            color: #333;
            text-align: center;
            font-weight: 500;
        }
        
        .config-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #495057;
        }
        
        .mobile-frame {
            max-width: 375px;
            margin: 0 auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
        }
        
        .mobile-screen {
            background: white;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .mobile-header {
            background: #007AFF;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
        }
        
        .mobile-content {
            padding: 20px;
        }
        
        .pc-frame {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }
        
        .legend {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .legend h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .legend ul {
            list-style: none;
            padding-left: 0;
        }
        
        .legend li {
            margin-bottom: 5px;
            color: #424242;
        }
        
        .legend li::before {
            content: "• ";
            color: #1976d2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎯 金刚位分组配置演示</h1>
            <p>基于实际数据库数据的金刚位分组展示效果 - 展示分组如何控制不同平台的布局和内容</p>
        </div>
        
        <div class="legend">
            <h3>📋 分组表的作用说明</h3>
            <ul>
                <li><strong>分组名称</strong>：在管理后台显示，用户端不直接展示</li>
                <li><strong>布局控制</strong>：通过 layout_config 控制网格布局（行数、列数、间距）</li>
                <li><strong>数量限制</strong>：max_display_count 限制每组最多显示的项目数</li>
                <li><strong>平台适配</strong>：同一功能在不同平台有不同的展示配置</li>
                <li><strong>分组类型</strong>：recommend（推荐）、default（常用功能）等业务分类</li>
            </ul>
        </div>
        
        <div class="platform-tabs">
            <button class="platform-tab active" onclick="showPlatform('ios')">📱 iOS</button>
            <button class="platform-tab" onclick="showPlatform('android')">🤖 Android</button>
            <button class="platform-tab" onclick="showPlatform('pc')">💻 PC Web</button>
            <button class="platform-tab" onclick="showPlatform('miniprogram')">🔗 小程序</button>
        </div>
        
        <!-- iOS 平台演示 -->
        <div id="ios" class="platform-demo active">
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">乐享生活 - iOS</div>
                    <div class="mobile-content">
                        <!-- 金刚位网格 (后台分组ID: 1 - 首页推荐) -->
                        <div class="grid-container grid-4">
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">📚</div>
                                    <div class="grid-title">课程预约</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">👨‍🏫</div>
                                    <div class="grid-title">教练咨询</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">🏋️</div>
                                    <div class="grid-title">器材租赁</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">👤</div>
                                    <div class="grid-title">会员中心</div>
                                </div>
                        </div>
                        <div class="config-display">
                            后台分组配置: 分组1(首页推荐) - {"rows": 2, "columns": 4, "spacing": 10}<br>
                            平台: ios | 状态: 启用 | 最大显示: 8项
                        </div>
                        
                        <!-- 金刚位网格 (后台分组ID: 5 - 常用功能) -->
                        <div class="grid-container grid-3" style="margin-top: 20px;">
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">💰</div>
                                    <div class="grid-title">充值缴费</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">📊</div>
                                    <div class="grid-title">数据统计</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">⚙️</div>
                                    <div class="grid-title">系统设置</div>
                                </div>
                        </div>
                        <div class="config-display">
                            后台分组配置: 分组5(常用功能) - {"rows": 2, "columns": 3, "spacing": 10}<br>
                            平台: ios | 状态: 启用 | 最大显示: 6项
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Android 平台演示 -->
        <div id="android" class="platform-demo">
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">乐享生活 - Android</div>
                    <div class="mobile-content">
                        <!-- 金刚位网格 (后台分组ID: 2 - 首页推荐) -->
                        <div class="grid-container grid-4">
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">📚</div>
                                    <div class="grid-title">课程预约</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">👨‍🏫</div>
                                    <div class="grid-title">教练咨询</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">🏋️</div>
                                    <div class="grid-title">器材租赁</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">👤</div>
                                    <div class="grid-title">会员中心</div>
                                </div>
                        </div>
                        <div class="config-display">
                            后台分组配置: 分组2(首页推荐) - {"rows": 2, "columns": 4, "spacing": 10}<br>
                            平台: android | 状态: 启用 | 最大显示: 8项
                        </div>
                        
                        <!-- 金刚位网格 (后台分组ID: 6 - 常用功能) -->
                        <div class="grid-container grid-3" style="margin-top: 20px;">
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">💰</div>
                                    <div class="grid-title">充值缴费</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">📊</div>
                                    <div class="grid-title">数据统计</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">⚙️</div>
                                    <div class="grid-title">系统设置</div>
                                </div>
                        </div>
                        <div class="config-display">
                            后台分组配置: 分组6(常用功能) - {"rows": 2, "columns": 3, "spacing": 10}<br>
                            平台: android | 状态: 启用 | 最大显示: 6项
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- PC 平台演示 -->
        <div id="pc" class="platform-demo">
            <div class="pc-frame">
                <div class="mobile-header">乐享生活 - PC Web</div>
                <div style="padding: 20px;">
                    <!-- 金刚位网格 (后台分组ID: 3 - 首页推荐) -->
                    <div class="grid-container grid-5">
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">📚</div>
                                <div class="grid-title">课程预约</div>
                            </div>
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">👨‍🏫</div>
                                <div class="grid-title">教练咨询</div>
                            </div>
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">🏋️</div>
                                <div class="grid-title">器材租赁</div>
                            </div>
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">👤</div>
                                <div class="grid-title">会员中心</div>
                            </div>
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">💰</div>
                                <div class="grid-title">充值缴费</div>
                            </div>
                    </div>
                    <div class="config-display">
                        后台分组配置: 分组3(首页推荐) - {"rows": 2, "columns": 5, "spacing": 15}<br>
                        平台: pc | 状态: 启用 | 最大显示: 10项
                    </div>
                    
                    <!-- 金刚位网格 (后台分组ID: 7 - 常用功能) -->
                    <div class="grid-container grid-4" style="margin-top: 20px;">
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">📊</div>
                                <div class="grid-title">数据统计</div>
                            </div>
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">⚙️</div>
                                <div class="grid-title">系统设置</div>
                            </div>
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">📋</div>
                                <div class="grid-title">订单管理</div>
                            </div>
                            <div class="grid-item">
                                <div class="grid-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">👥</div>
                                <div class="grid-title">用户管理</div>
                            </div>
                    </div>
                    <div class="config-display">
                        后台分组配置: 分组7(常用功能) - {"rows": 2, "columns": 4, "spacing": 15}<br>
                        平台: pc | 状态: 启用 | 最大显示: 8项
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 小程序平台演示 -->
        <div id="miniprogram" class="platform-demo">
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">乐享生活 - 小程序</div>
                    <div class="mobile-content">
                        <!-- 金刚位网格 (后台分组ID: 4 - 首页推荐) -->
                        <div class="grid-container grid-4">
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">📚</div>
                                    <div class="grid-title">课程预约</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a56, #ff6b6b);">👨‍🏫</div>
                                    <div class="grid-title">教练咨询</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">🏋️</div>
                                    <div class="grid-title">器材租赁</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ffd93d, #ff9a56);">👤</div>
                                    <div class="grid-title">会员中心</div>
                                </div>
                        </div>
                        <div class="config-display">
                            后台分组配置: 分组4(首页推荐) - {"rows": 2, "columns": 4, "spacing": 10}<br>
                            平台: miniprogram | 状态: 启用 | 最大显示: 8项
                        </div>
                        
                        <!-- 金刚位网格 (后台分组ID: 8 - 常用功能) -->
                        <div class="grid-container grid-3" style="margin-top: 20px;">
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">💰</div>
                                    <div class="grid-title">充值缴费</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">📊</div>
                                    <div class="grid-title">数据统计</div>
                                </div>
                                <div class="grid-item">
                                    <div class="grid-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">⚙️</div>
                                    <div class="grid-title">系统设置</div>
                                </div>
                        </div>
                        <div class="config-display">
                            后台分组配置: 分组8(常用功能) - {"rows": 2, "columns": 3, "spacing": 10}<br>
                            平台: miniprogram | 状态: 启用 | 最大显示: 6项
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showPlatform(platform) {
            // 隐藏所有平台演示
            document.querySelectorAll('.platform-demo').forEach(demo => {
                demo.classList.remove('active');
            });
            
            // 显示选中的平台
            document.getElementById(platform).classList.add('active');
            
            // 更新标签状态
            document.querySelectorAll('.platform-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
        }
    </script>
</body>
</html>